package com.ecco.webApi.users;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.users.commands.UserCommand;
import com.ecco.users.repository.UserCommandRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.evidence.ExtractCommandViewModelJson;
import com.ecco.webApi.viewModels.Result;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/** see web-api-paths.txt for logic of structure */
@RestController
@Secured("ROLE_ADMINLOGIN")
public class UserCommandController extends BaseWebApiController {

    @NonNull
    private ExtractCommandViewModelJson<UserCommand> extractJsonBody;

    @NonNull
    private UserCoreCommandHandler userCoreCommandHandler;

    @NonNull
    private UserAclCommandHandler userAclCommandHandler;

    @NonNull
    private UserMfaResetCommandHandler userMfaResetCommandHandler;

    @NonNull
    private UserCommandRepository userCommandRepository;

    @Autowired
    public UserCommandController(
            @NonNull ExtractCommandViewModelJson extractJsonBody,
            @NonNull UserCoreCommandHandler userCoreCommandHandler,
            @NonNull UserAclCommandHandler userAclCommandHandler,
            @NonNull UserMfaResetCommandHandler userMfaResetCommandHandler,
            @NonNull UserCommandRepository userCommandRepository) {
        this.extractJsonBody = extractJsonBody;
        this.userCommandRepository = userCommandRepository;
        this.userCoreCommandHandler = userCoreCommandHandler;
        this.userAclCommandHandler = userAclCommandHandler;
        this.userMfaResetCommandHandler = userMfaResetCommandHandler;
    }

    @GetJson("/users/{userIdSubject}/commands/")
    public CharSequence getUserCommands(@PathVariable long userIdSubject) { // CharSequence, not Object due to https://github.com/spring-projects/spring-hateoas/issues/1638

        List<UserCommand> commands = userCommandRepository.findAllByUserIdSubject(userIdSubject);

        CharSequence joinedJson = commands.stream().map(extractJsonBody).collect(Collectors.joining(","));
        // TODO: Could go with Stream.of to allow lazy eval if we can return a stream
        return "[" + joinedJson + "]";
    }

    @GetJson("/users/command/{uuid}")
    public String findCommandByUuid(@PathVariable UUID uuid) {
        var command = userCommandRepository.findOneByUuid(uuid);
        return extractJsonBody.apply(command).toString();
    }

    @WriteableTransaction
    @PostJsonReturningJson({"/users/-/commands/", "/users/{userIdSubject}/commands/"})
    public Result updateUser(
            UserParams params,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return userCoreCommandHandler.handleCommand(authentication, params, requestBody);
    }

    @WriteableTransaction
    @PostJsonReturningJson("/users/{userIdSubject}/commands/acls/")
    public Result updateUserAcls(
            UserParams params,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return userAclCommandHandler.handleCommand(authentication, params, requestBody);
    }

    @WriteableTransaction
    @PostJsonReturningJson("/users/{userIdSubject}/commands/resetMfa/")
    public Result resetUserMfaSecret(
            UserParams params,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return userMfaResetCommandHandler.handleCommand(authentication, params, requestBody);
    }

}
