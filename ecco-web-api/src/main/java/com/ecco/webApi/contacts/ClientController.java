package com.ecco.webApi.contacts;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import org.jspecify.annotations.NonNull;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.webApi.annotations.DeleteJson;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import com.ecco.dao.ClientRepository;
import com.ecco.dom.ClientDetail;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.DelegateResponse;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.service.ClientDetailService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.google.common.collect.Maps;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class ClientController extends BaseWebApiController {

    private final ClientRepository clientRepository;
    private final ClientDetailService clientDetailService;
    private final ClientToViewModel clientToViewModel;
    private ClientDefinitionToViewModel clientDefinitionToViewModel;

    private final ClientFromViewModel clientFromViewModel;
    private final ClientDefinitionFromViewModel clientDefinitionFromViewModel = new ClientDefinitionFromViewModel();
    private final ClientWebService clientWebService;

    private final Function<DelegateResponse<Iterable<ClientDefinition>>, DelegateResponse<Iterable<ClientViewModel>>> clientDefinitionResponseToViewModel
        = (@NonNull DelegateResponse<Iterable<ClientDefinition>> input) -> {
        if (input.getPayload() == null) {
                return DelegateResponse.emptyFrom(input);
            }
            else {
                Iterable<ClientViewModel> result = StreamSupport.stream(input.getPayload().spliterator(), false)
                        .map(clientDefinitionToViewModel::apply)
                        .collect(Collectors.toList());
                return DelegateResponse.ok(result);
            }
        };

    public ClientController(ClientRepository clientRepository, ClientDetailService clientDetailService,
                            ClientFromViewModel clientFromViewModel, ClientToViewModel clientToViewModel,
                            ClientWebService clientWebService, ListDefinitionRepository listDefinitionRepository) {
        this.clientRepository = clientRepository;
        this.clientDetailService = clientDetailService;
        this.clientToViewModel = clientToViewModel;
        this.clientFromViewModel = clientFromViewModel;
        this.clientWebService = clientWebService;
        this.clientDefinitionToViewModel = new ClientDefinitionToViewModel(listDefinitionRepository);
    }

    // NB Duplicated in ReferralBaseController to avoid a ClientBaseController
    @GetJson("/clients/{id}/")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    public ClientViewModel findOne(@PathVariable long id) {
        return clientToViewModel.apply(clientRepository.findById(id).orElse(null));
    }

    @GetJson("/clients/")
    public Stream<ClientViewModel> listClients(
        @RequestParam(value = "srId", required = false) int[] serviceRecipientIds,
        @RequestParam(value = "id", required = false) long[] clientIds
    ) {
        List<ClientDetail> clients =
            serviceRecipientIds != null ? clientRepository.findAllByServiceRecipientId(serviceRecipientIds)
            : clientIds != null ? clientRepository.findAllById(clientIds)
            : clientRepository.findAll();
        return clients
                .stream()
                .map(clientToViewModel::apply);
    }

    /**
     * Queries across internal sources of client data only.
     * @return map of externalsystem name to a list of matching clients. Local clients are identified by key 'ecco'.
     */
    @PostJsonReturningJson("/clients/local/query")
    public Map<String, DelegateResponse<Iterable<ClientViewModel>>> queryLocalClientsByExample(@RequestBody ClientViewModel exemplar) {
        // local query based on ClientDefinition - from ClientViewModel
        final Map<String,DelegateResponse<Iterable<ClientDefinition>>> map
                = clientDetailService.queryClientsByExample(clientDefinitionFromViewModel.apply(exemplar), false);

        // put back to ClientViewModel - in a limited way, we just pick the things to show in a search bar
        return Maps.transformValues(map, clientDefinitionResponseToViewModel::apply);
    }

    /**
     * Queries across internal and external sources of client data.
     * @return map of externalsystem name to a list of matching clients. Local clients are identified by key 'ecco'.
     */
    @PostJsonReturningJson("/clients/all/query")
    public Map<String, DelegateResponse<Iterable<ClientViewModel>>> queryAllClientsByExample(@RequestBody ClientViewModel exemplar) {
        // query based on the ClientDefinition - from ClientViewModel
        final Map<String,DelegateResponse<Iterable<ClientDefinition>>> map
                = clientDetailService.queryClientsByExample(clientDefinitionFromViewModel.apply(exemplar), true);

        // put back to ClientViewModel
        return Maps.transformValues(map, clientDefinitionResponseToViewModel::apply);
    }


    @GetJson("/clients/fromExternal/{externalSourceName}/{externalRef}")
    public ClientViewModel getFromExternal(
            @PathVariable String externalSourceName,
            @PathVariable String externalRef) {
        ClientDefinition exemplar = ClientDefinition.BuilderFactory.create()
                .externalClientSource(externalSourceName)
                .externalClientRef(externalRef)
                .build();
        Map<String, DelegateResponse<Iterable<ClientDefinition>>> clientsBySource = clientDetailService.queryClientsByExample(exemplar, true);
        DelegateResponse<Iterable<ClientDefinition>> results = clientsBySource.get(externalSourceName);
        if (results == null) {
            throw new RuntimeException("No data source with name: " + externalSourceName + " is configured in ECCO.");
        }

        if (results.getStatusCode() >= 400) {
            throw new RuntimeException(results.getStatusText());
        }

        Iterator<ClientDefinition> iterator = results.getPayload().iterator();
        if (iterator.hasNext()) {
           return clientDefinitionToViewModel.apply(iterator.next());
        }
        throw new RuntimeException("Could not find record with reference " + externalRef + " in " + externalSourceName
                + ". Please contact support and ask for configurations to be checked.");
    }

    @GetJson("/referrals/{referralId}/client/")
    public ClientViewModel findOneByReferralId(@PathVariable long referralId) {
        return clientToViewModel.apply(clientRepository.findOneByReferralId(referralId));
    }

    @GetJson("/serviceRecipients/{serviceRecipientId}/client/")
    public ClientViewModel findOneByServiceRecipientId(@PathVariable int serviceRecipientId) {
        return clientToViewModel.apply(clientRepository.findOneByServiceRecipientId(serviceRecipientId));
    }

    /**
     * Create a client, and save a command
     */
    @WriteableTransaction
    @PostJson("/clients/")
    public Result create(@RequestBody ClientViewModel clientViewModel, @RequestParam(defaultValue = "false") boolean enforceUniqueClient) {
        return clientWebService.create(clientViewModel, enforceUniqueClient);
    }

    /**
     * Find clients of the same code
     */
    @GetJson(value = "/clients/byCode/{code}/")
    @ResponseStatus(HttpStatus.OK)
    public Stream<ClientViewModel> findByCode(@SuppressWarnings("unused") @PathVariable String code) {
        List<ClientDetail> matchingClients = clientRepository.findAllByCode(code);
        return matchingClients
                .stream()
                .map(clientToViewModel::apply);
    }

    /**
     * Find clients of the external client ref
     */
    @GetJson(value = "/clients/byExternalRef/{code}/")
    @ResponseStatus(HttpStatus.OK)
    public Stream<ClientViewModel> findByExternalRef(@SuppressWarnings("unused") @PathVariable String code) {
        List<ClientDetail> matchingClients = clientRepository.findAllByExternalClientRef(code);
        return matchingClients
                .stream()
                .map(clientToViewModel::apply);
    }

    /**
     * Update a client with several properties from the view model
     * For now we carefully control which properties and circumstances the data is updated
     */
    @PutMapping(value = "/clients/byCode/{code}/", consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public Result update(@SuppressWarnings("unused") @PathVariable String code,
                         @RequestBody ClientViewModel clientViewModel) {
        Assert.notNull(clientViewModel.code, "code is required in clientViewModel");

        List<ClientDetail> matchingClients = clientRepository.findAllByCode(clientViewModel.code);
        if (matchingClients.size() != 1) {
            throw new RuntimeException("Incorrect client count, need 1 but found " + matchingClients.size() + " from referralCode: " + clientViewModel.code);
        }

        ClientDetail cd = matchingClients.get(0);
        final ClientDetail cdIn = clientFromViewModel.apply(clientViewModel);

        // apply desired properties - if nothing already applied
        if (cd.getContact().getAddress().getCounty() == null) {
            cd.getContact().getAddress().setCounty(cdIn.getContact().getAddress().getCounty());
        }

        clientRepository.save(cd);
        return new Result(cd.getId());
    }

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @DeleteJson("/clients/{id}/")
    public Result delete(@PathVariable Long id, @RequestBody ClientViewModel clientViewModel) {
        Assert.isNull(clientViewModel.clientId, "No id should be set in request body on DELETE");
        Assert.notNull(clientViewModel.lastName, "Must specify lastName when deleting a client");

        ClientDetail existing = clientRepository.findById(id).orElse(null);

        Assert.notNull(existing, "No client found with that id, you may need to check for c-id not code");
        Assert.state(clientViewModel.lastName.equals(existing.getContact().getLastName()), "The supplied lastName did not match");

        clientRepository.deleteById(id);
        return new Result("deleted");
    }

    /**
     * Move a client to the 'hidden' collection.
     * It will then not appear on any normal queries for clients.
     * This does not update any other properties of the client.
     *
     * @param clientViewModel the client to hide, only the clientId is relevant
     */
    @PostJson("/hidden/clients/")
    @ResponseStatus(HttpStatus.OK)
    @WriteableTransaction
    public Result hide(@RequestBody ClientViewModel clientViewModel) {
        Assert.notNull(clientViewModel.clientId, "clientId is required on body of the POST");

        ClientDetail existing = clientRepository.findById(clientViewModel.clientId).orElse(null);
        Assert.notNull(existing, "clientId must match an existing non-hidden client");
        existing.requestDelete();
        clientRepository.save(existing);
        return new Result("hidden", clientViewModel.clientId);
    }

}
