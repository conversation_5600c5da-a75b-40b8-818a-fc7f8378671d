package com.ecco.webApi.contacts;

import com.ecco.dao.commands.ContactsCommandRepository;
import com.ecco.dom.commands.ContactCommand;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.evidence.ExtractCommandViewModelJson;
import com.ecco.webApi.viewModels.Result;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

/** This is the C part of Command Query Responsibility Separation and can be pushed into an AuditFirst module */
@RestController
public class ContactsCommandController extends BaseWebApiController {

    @NonNull
    private final ContactsCommandRepository contactsCommandRepository;

    @NonNull
    private final ContactCommandHandler contactCommandHandler;

    @NonNull
    private final ContactAddressCommandHandler contactAddressCommandHandler;

    @NonNull
    private final ExtractCommandViewModelJson<ContactCommand> extractJsonBody;

    @NonNull
    private final ContactCalendarEntryCommandHandler contactCalendarEntryCommandHandler;


    @Autowired
    public ContactsCommandController(
            @NonNull ContactsCommandRepository commandRepository,
            @NonNull ContactCalendarEntryCommandHandler contactCalendarEntryCommandHandler,
            @NonNull ContactCommandHandler contactCommandHandler,
            @NonNull ContactAddressCommandHandler contactAddressCommandHandler,
            @NonNull ExtractCommandViewModelJson<ContactCommand> extractJsonBody
    ) {
        this.contactsCommandRepository = commandRepository;
        this.contactCalendarEntryCommandHandler = contactCalendarEntryCommandHandler;
        this.contactCommandHandler = contactCommandHandler;
        this.contactAddressCommandHandler = contactAddressCommandHandler;
        this.extractJsonBody = extractJsonBody;
    }


    @WriteableTransaction
    @PostJson("/contact/{contactId}/calendar-entry/command/")
    @ResponseStatus(HttpStatus.OK)
    public Result calendarEntryCommand(
            @NonNull Authentication authentication,
            @NonNull ContactCommandParams params,
            @NonNull @RequestBody String requestBody) throws IOException {

        return contactCalendarEntryCommandHandler.handleCommand(authentication, params, requestBody);
    }

    @GetJson("/contacts/command/{uuid}/")
    public String findCommandByUuid(@PathVariable UUID uuid) {
        var command = contactsCommandRepository.findOneByUuid(uuid);
        return extractJsonBody.apply(command).toString();
    }

    /**
     * @return the JSON as a string array (using the same JSON we stored going in)
     */
    @GetJson("/contacts/{contactId}/commands/")
    public String findCommandsByContactId(
//            @Nonnull Authentication authentication,
            @PathVariable int contactId) {

        List<ContactCommand> commands = contactsCommandRepository.findAllUpdatesByContactId(contactId);
        return extractJsonBody.asJsonArray(commands);
    }

    @WriteableTransaction
    @PostJsonReturningJson("/contacts/{contactId}/commands/")
    public Result updateContact(
            @PathVariable int contactId,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return contactCommandHandler.handleCommand(authentication, contactId, requestBody);
    }

    @WriteableTransaction
    @PostJsonReturningJson("/contacts/{contactId}/address/commands/")
    public Result updateContactAddress(
            @PathVariable int contactId,
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return contactAddressCommandHandler.handleCommand(authentication, contactId, requestBody);
    }
}
