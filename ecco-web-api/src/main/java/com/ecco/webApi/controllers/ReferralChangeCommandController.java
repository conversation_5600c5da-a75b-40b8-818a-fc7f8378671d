package com.ecco.webApi.controllers;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.PropertyAccessor;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.ecco.dao.ReferralRepository;
import com.ecco.dom.Referral;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.health.HealthEndpoint;
import com.ecco.webApi.viewModels.ChoiceUpdateCommand;
import com.ecco.webApi.viewModels.DateTimeUpdateCommand;
import com.ecco.webApi.viewModels.DateUpdateCommand;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.TextUpdateCommand;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class ReferralChangeCommandController extends BaseWebApiController {

    private final ReferralRepository referralRepository;

    @PersistenceContext
    protected EntityManager entityManager;

    @Autowired
    public ReferralChangeCommandController(ReferralRepository referralRepository) {
        this.referralRepository = referralRepository;
    }


    @WriteableTransaction
    @HealthEndpoint("referral.textUpdate")
    @PostJson("/referrals/{id}/textUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result processReferralTextUpdateCommand(
            @RequestBody TextUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        Referral referral = referralRepository.findOneForSimpleUpdate(id);
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(referral);

        String currentValue = (String) wrapper.getPropertyValue(path);
        AttributeChangeUtility.checkAndHandleNonMatchingTextValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue);
        referralRepository.save(referral);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

    @WriteableTransaction
    @HealthEndpoint("referral.choiceUpdate")
    @PostJson("/referrals/{id}/choiceUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result processReferralChoiceUpdateCommand(
            @RequestBody ChoiceUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        Referral referral = referralRepository.findOneForSimpleUpdate(id);
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(referral);

        Integer currentValue = (Integer) wrapper.getPropertyValue(path);
        AttributeChangeUtility.checkAndHandleNonMatchingIntegerValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue);
        referralRepository.save(referral);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

    @WriteableTransaction
    @HealthEndpoint("referral.dateUpdate")
    @PostJson("/referrals/{id}/dateUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result processReferralDateUpdateCommand(
            @RequestBody DateUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        Referral referral = referralRepository.findOneForSimpleUpdate(id);
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(referral);

        LocalDate currentValue = (LocalDate) wrapper.getPropertyValue(path);
        AttributeChangeUtility.checkAndHandleNonMatchingDateValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue);
        referralRepository.save(referral);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

    /**
     * We use this currently for acceptance testing to set a system field - so we don't check for previous values.
     * This method assumes the local DateTime provided are meant to be UTC
     */
    @WriteableTransaction
    @HealthEndpoint("referral.dateTimeUpdate")
    @PostJson("/referrals/{id}/dateTimeUpdate/{path}/")
    @ResponseStatus(HttpStatus.OK) // note: use ACCEPTED if we make it async
    public Result overrideReferralDateTime(
            @RequestBody DateTimeUpdateCommand updateCommand,
            @PathVariable Long id,
            @PathVariable String path) {

        Referral referral = referralRepository.findOneForSimpleUpdate(id);
        PropertyAccessor wrapper = PropertyAccessorFactory.forBeanPropertyAccess(referral);

        LocalDateTime currentValue = ((DateTime) wrapper.getPropertyValue(path)).toLocalDateTime();
        //checkAndHandleNonMatchingDateTimeValue(currentValue, updateCommand);

        wrapper.setPropertyValue(path, updateCommand.newValue.toDateTime(DateTimeZone.UTC));
        referralRepository.save(referral);
        entityManager.flush(); // to trigger validation
        return new Result("updated");
    }

}
