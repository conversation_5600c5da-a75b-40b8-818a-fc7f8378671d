package com.ecco.webApi.controllers;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dom.ClientDetailAbstract;
import com.ecco.dom.hr.WorkerJob;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.webApi.contacts.WorkerJobToViewModel;
import com.ecco.webApi.contacts.WorkerJobViewModel;
import com.ecco.webApi.contacts.WorkerToViewModel;
import com.ecco.webApi.contacts.WorkerViewModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class WorkerJobController extends BaseWebApiController {

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private final ObjectMapper objectMapper;
    private final CreateServiceRecipientCommandController createServiceRecipientCommandController;
    private final WorkerJobRepository workerJobRepository;
    private final WorkerJobToViewModel workerJobToViewModel = new WorkerJobToViewModel();
    private final WorkerToViewModel workerToViewModelWithoutJobs;

    public WorkerJobController(CreateServiceRecipientCommandController createServiceRecipientCommandController,
                               ObjectMapper objectMapper,
                               WorkerJobRepository workerJobRepository,
                               ListDefinitionRepository listDefinitionRepository) {
        this.createServiceRecipientCommandController = createServiceRecipientCommandController;
        this.objectMapper = objectMapper;
        this.workerJobRepository = workerJobRepository;
        this.workerToViewModelWithoutJobs = new WorkerToViewModel(listDefinitionRepository, false);
    }

    @GetJson("/workerJob/{id}/")
    public WorkerJobViewModel findOneWorkerJob(@PathVariable int id) {
        // TODO verifyAccess(referral);
        return this.workerJobToViewModel.apply(this.workerJobRepository.getById(id));
    }
    public static Pattern findOneWorkerJobPattern = Pattern.compile("workerJob/(\\d+)/");
    public static final Function<String, Integer> EXTRACT_ID_FN = (href) -> {
        Matcher matches = findOneWorkerJobPattern.matcher(href);
        matches.find();
        String rId = matches.group(1);
        return Integer.parseInt(rId);
    };

    /**
     * Return worker jobs where employed at date, from contactIds.
     * See findWorkersEmployedAtByContactIds below, since the WorkerJobViewModel returned here is not that helpful.
     * @param contactIds typically those from the workers who have access to a service/project required.
     */
    @GetJson("/workerJob/employedAt/{employedAt}/byContactIds/")
    public Iterable<WorkerJobViewModel> findWorkerJobsEmployedAtByContactIds(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) java.time.LocalDate employedAt,
                                                                             @RequestParam(name = "ids") List<Long> contactIds) {
        return StreamSupport.stream(
                workerJobRepository.findAllStaffWithIndividualIdEmployedAt(contactIds, EccoTimeUtils.convertFromUsersLocalDateTime(employedAt.atStartOfDay())).spliterator(), false)
                .map(workerJobToViewModel)
                .collect(Collectors.toList());
    }

    /**
     * Return workers from contactIds with only jobs employed at.
     * As per findWorkerJobsEmployedAtByContactIds above, but this returns the WorkerViewModel
     * @param contactIds typically those from the workers who have access to a service/project required.
     */
    @GetJson("/workers/employedAt/{employedAt}/byContactIds/")
    public Iterable<WorkerViewModel> findWorkersEmployedAtByContactIds(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) java.time.LocalDate employedAt,
                                                                       @RequestParam(name = "ids") List<Long> contactIds) {
        // get the jobs and the unique workers
        var matchedJobs = workerJobRepository.findAllStaffWithIndividualIdEmployedAt(contactIds, EccoTimeUtils.convertFromUsersLocalDateTime(employedAt.atStartOfDay()));
        var workers = StreamSupport.stream(matchedJobs.spliterator(), false)
                .map(WorkerJob::getWorker)
                .filter(distinctByKey(ClientDetailAbstract::getId));

        // for each worker, apply the relevant jobs
        return workers.map(w -> {
            var vm = workerToViewModelWithoutJobs.apply(w);
            assert vm != null;
            var jobs = StreamSupport.stream(matchedJobs.spliterator(), false)
                    .filter(j -> j.getWorker().getId().equals(w.getId()))
                    .map(workerJobToViewModel)
                    .toList();
            vm.setJobs(jobs);
            return vm;
        }).toList();
    }

}
