import {StringToStringMap} from "@eccosolutions/ecco-common";
import {Person<PERSON><PERSON>Fields, PersonSecretFields} from "./contact-dto";

export interface Client<PERSON>lainFields extends PersonPlainFields {
    /** The client ID. */
    clientId?: number | undefined;

    /** The client's Contact ID, used to identify the client's contact details
     * in Cosmo. */
    contactId: number;

    /** The building ID of where this client is residing. */
    residenceId: number | null;
}

export interface ClientDetailAbstractSecretFields extends PersonSecretFields {
    /** A custom identifier used by the organisation to uniquely identify the
     * client.
     *
     * This might, for example, be an identifier originating from a
     * legacy system. */
    code: string;

    /** The client's NHS number. */
    nhs: string;

    /** The client's National Insurance number. */
    ni: string;

    textMap?: StringToStringMap | undefined;

    dateMap?: StringToStringMap | undefined;

    communicationNeeds?: string | undefined;

    description?: string | undefined;

    doctorDetails?: string | undefined;

    dentistDetails?: string | undefined;

    emergencyKeyCode?: string | undefined;

    /** The client's emergency key word, or null if the client does not have one. */
    emergencyKeyWord?: string | undefined;

    emergencyDetails?: string | undefined;

    medicationDetails?: string | undefined;

    risksAndConcerns?: string | undefined;

    /** date-time the form was completed with the client (ie ignore blank details) */
    completeAt?: string | undefined;

    /** The client's preferred contact information, or null if there is no
     * contact information for the client.
     *
     * For example, if the client prefers to be contacted by telephone, this
     * field contains the client's telephone number. */
    preferredContactInfo?: string | undefined;
}

export interface ClientSecretFields extends ClientDetailAbstractSecretFields {
    /** The client's housing benefit . */
    housingBenefit: string;

    /** The name of the residence where the residing client resides */
    residenceName: string;

}

/** A client.
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.ClientViewModel. */
export type Client = ClientPlainFields & ClientSecretFields;

/** This matches ClientViewModel but with limited fields actually used */
export interface PersonSearchCriteriaDto {
    firstName?: string | null | undefined;
    lastName?: string | null | undefined;
    /** Search for this text as code, c-id, r-id or externalSystemRef */
    code?: string | undefined;

    /** Can use first element of address, and postcode for prefix search */
    address?:
        | {
              address?: [string] | undefined;
              postcode?: string | undefined;
          }
        | undefined;
}

export function clientDisplayName(client: Client): string {
    return `${client.firstName} ${client.lastName} ${client.knownAs ? `(${client.knownAs})` : ""}`;
}
